import { useState } from 'react'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * QuickEventServiceSelector component for streamlined service and tier selection
 *
 * @param {Object} props - Component props
 * @param {Array} props.services - Array of services with pricing tiers
 * @param {Function} props.onServiceSelect - Callback when service and tier are selected
 * @returns {JSX.Element}
 */
export default function QuickEventServiceSelector({ services, onServiceSelect }) {
  const [selectedServiceId, setSelectedServiceId] = useState(null)
  const [selectedTierId, setSelectedTierId] = useState(null)

  // Filter services suitable for quick events (typically face painting, quick services)
  const getQuickEventServices = () => {
    return services.filter(service => {
      // Include services that are visible on events or have quick tiers
      return service.visible_on_events || 
             service.category?.toLowerCase().includes('face') ||
             service.category?.toLowerCase().includes('paint') ||
             service.name?.toLowerCase().includes('face') ||
             service.name?.toLowerCase().includes('paint') ||
             service.name?.toLowerCase().includes('glitter') ||
             (service.pricing_tiers && service.pricing_tiers.some(tier => tier.duration <= 60))
    })
  }

  // Get category icon based on service category
  const getCategoryIcon = (category) => {
    const iconMap = {
      'face painting': '🎨',
      'painting': '🎨',
      'makeup': '💄',
      'hair': '💇‍♀️',
      'nails': '💅',
      'massage': '💆‍♀️',
      'skincare': '✨',
      'photography': '📸',
      'entertainment': '🎭',
      'general': '⭐'
    }
    return iconMap[category?.toLowerCase()] || iconMap.general
  }

  // Get pricing tiers, fallback to service base price if no tiers
  const getPricingTiers = (service) => {
    if (service.pricing_tiers && service.pricing_tiers.length > 0) {
      return service.pricing_tiers.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
    }
    
    // Fallback: create a single tier from service base data
    return [{
      id: `fallback-${service.id}`,
      name: 'Standard',
      description: 'Standard service duration and pricing',
      duration: service.duration || 60,
      price: service.price || 0,
      is_default: true,
      sort_order: 1
    }]
  }

  const formatDuration = (minutes) => {
    if (minutes < 60) {
      return `${minutes} min`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    if (remainingMinutes === 0) {
      return `${hours}h`
    }
    return `${hours}h ${remainingMinutes}m`
  }

  const handleServiceTierSelect = (service, tier) => {
    setSelectedServiceId(service.id)
    setSelectedTierId(tier.id)
    
    // Small delay for visual feedback before proceeding
    setTimeout(() => {
      onServiceSelect(service, tier)
    }, 150)
  }

  const quickEventServices = getQuickEventServices()

  if (quickEventServices.length === 0) {
    return (
      <div className={styles.quickEventSelector}>
        <div className={styles.quickEventHeader}>
          <h2 className={styles.quickEventTitle}>Quick Event Services</h2>
          <p className={styles.quickEventSubtitle}>No quick event services available</p>
        </div>
        <div className={styles.noQuickServices}>
          <div className={styles.noServicesIcon}>⚡</div>
          <h3>No Quick Event Services</h3>
          <p>Please configure services for quick events in the inventory section.</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.quickEventSelector}>
      <div className={styles.quickEventHeader}>
        <h2 className={styles.quickEventTitle}>Quick Event Services</h2>
        <p className={styles.quickEventSubtitle}>Select service and duration for immediate payment</p>
      </div>

      <div className={styles.quickServiceGrid}>
        {quickEventServices.map((service) => {
          const icon = getCategoryIcon(service.category)
          const pricingTiers = getPricingTiers(service)

          return (
            <div key={service.id} className={styles.quickServiceCard}>
              <div className={styles.quickServiceHeader}>
                <span className={styles.quickServiceIcon}>{icon}</span>
                <h3 className={styles.quickServiceName}>
                  {safeRender(service.name, 'Unnamed Service')}
                </h3>
              </div>

              <div className={styles.quickTierGrid}>
                {pricingTiers.map((tier) => {
                  const isSelected = selectedServiceId === service.id && selectedTierId === tier.id
                  const isRecommended = tier.is_default || pricingTiers.length > 1 && pricingTiers.indexOf(tier) === 1

                  return (
                    <button
                      key={tier.id}
                      className={`${styles.quickTierButton} ${isRecommended ? styles.recommended : ''} ${isSelected ? styles.selected : ''}`}
                      onClick={() => handleServiceTierSelect(service, tier)}
                    >
                      <div className={styles.quickTierName}>
                        {safeRender(tier.name, 'Tier')}
                      </div>
                      <div className={styles.quickTierDuration}>
                        {formatDuration(tier.duration)}
                      </div>
                      <div className={styles.quickTierPrice}>
                        ${parseFloat(tier.price || 0).toFixed(2)}
                      </div>
                      {tier.description && (
                        <div className={styles.quickTierDescription}>
                          {safeRender(tier.description, '')}
                        </div>
                      )}
                    </button>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
